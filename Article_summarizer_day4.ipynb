{"cells": [{"cell_type": "markdown", "metadata": {"id": "e5b9a7cb"}, "source": ["## Setup\n", "\n", "First, we need to install the necessary library and set up the Groq API client.\n", "\n", "Make sure you have your Groq API key stored in Colab secrets under the name `GROQ_API_KEY`."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "75e21770", "outputId": "1142080c-dfb3-4068-f080-ea67de9b9f78"}, "source": ["!pip install groq ipywidgets"], "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting groq\n", "  Downloading groq-0.30.0-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: ipywidgets in /usr/local/lib/python3.11/dist-packages (7.7.1)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from groq) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from groq) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from groq) (0.28.1)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from groq) (2.11.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in /usr/local/lib/python3.11/dist-packages (from groq) (4.14.1)\n", "Requirement already satisfied: ipykernel>=4.5.1 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (6.17.1)\n", "Requirement already satisfied: ipython-genutils~=0.2.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (0.2.0)\n", "Requirement already satisfied: traitlets>=4.3.1 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (5.7.1)\n", "Requirement already satisfied: widgetsnbextension~=3.6.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (3.6.10)\n", "Requirement already satisfied: ipython>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (7.34.0)\n", "Requirement already satisfied: jupyterlab-widgets>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (3.0.15)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->groq) (3.10)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (2025.7.14)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq) (0.16.0)\n", "Requirement already satisfied: debugpy>=1.0 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (1.8.15)\n", "Requirement already satisfied: jupyter-client>=6.1.12 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (6.1.12)\n", "Requirement already satisfied: matplotlib-inline>=0.1 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (0.1.7)\n", "Requirement already satisfied: nest-asyncio in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (1.6.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (25.0)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (5.9.5)\n", "Requirement already satisfied: pyzmq>=17 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (24.0.1)\n", "Requirement already satisfied: tornado>=6.1 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (6.4.2)\n", "Requirement already satisfied: setuptools>=18.5 in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (75.2.0)\n", "Collecting jedi>=0.16 (from ipython>=4.0.0->ipywidgets)\n", "  Downloading jedi-0.19.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (4.4.2)\n", "Requirement already satisfied: pickleshare in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (0.7.5)\n", "Requirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (3.0.51)\n", "Requirement already satisfied: pygments in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (2.19.2)\n", "Requirement already satisfied: backcall in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (0.2.0)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (4.9.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq) (0.4.1)\n", "Requirement already satisfied: notebook>=4.4.1 in /usr/local/lib/python3.11/dist-packages (from widgetsnbextension~=3.6.0->ipywidgets) (6.5.7)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.11/dist-packages (from jedi>=0.16->ipython>=4.0.0->ipywidgets) (0.8.4)\n", "Requirement already satisfied: jupyter-core>=4.6.0 in /usr/local/lib/python3.11/dist-packages (from jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (5.8.1)\n", "Requirement already satisfied: python-dateutil>=2.1 in /usr/local/lib/python3.11/dist-packages (from jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (2.9.0.post0)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (3.1.6)\n", "Requirement already satisfied: argon2-cffi in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (25.1.0)\n", "Requirement already satisfied: nbformat in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (5.10.4)\n", "Requirement already satisfied: nbconvert>=5 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (7.16.6)\n", "Requirement already satisfied: Send2Trash>=1.8.0 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.8.3)\n", "Requirement already satisfied: terminado>=0.8.3 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.18.1)\n", "Requirement already satisfied: prometheus-client in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.22.1)\n", "Requirement already satisfied: nbclassic>=0.4.7 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.3.1)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.11/dist-packages (from pexpect>4.3->ipython>=4.0.0->ipywidgets) (0.7.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.11/dist-packages (from prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0->ipython>=4.0.0->ipywidgets) (0.2.13)\n", "Requirement already satisfied: platformdirs>=2.5 in /usr/local/lib/python3.11/dist-packages (from jupyter-core>=4.6.0->jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (4.3.8)\n", "Requirement already satisfied: notebook-shim>=0.2.3 in /usr/local/lib/python3.11/dist-packages (from nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.2.4)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (4.13.4)\n", "Requirement already satisfied: bleach!=5.0.0 in /usr/local/lib/python3.11/dist-packages (from bleach[css]!=5.0.0->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (6.2.0)\n", "Requirement already satisfied: defusedxml in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.7.1)\n", "Requirement already satisfied: jupyterlab-pygments in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.3.0)\n", "Requirement already satisfied: markupsafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (3.0.2)\n", "Requirement already satisfied: mistune<4,>=2.0.3 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (3.1.3)\n", "Requirement already satisfied: nbclient>=0.5.0 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.10.2)\n", "Requirement already satisfied: pandocfilters>=1.4.1 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.5.1)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /usr/local/lib/python3.11/dist-packages (from nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2.21.1)\n", "Requirement already satisfied: jsonschema>=2.6 in /usr/local/lib/python3.11/dist-packages (from nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (4.25.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.1->jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (1.17.0)\n", "Requirement already satisfied: argon2-cffi-bindings in /usr/local/lib/python3.11/dist-packages (from argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (21.2.0)\n", "Requirement already satisfied: webencodings in /usr/local/lib/python3.11/dist-packages (from bleach!=5.0.0->bleach[css]!=5.0.0->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.5.1)\n", "Requirement already satisfied: tinycss2<1.5,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from bleach[css]!=5.0.0->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.4.0)\n", "Requirement already satisfied: attrs>=22.2.0 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.26.0)\n", "Requirement already satisfied: jupyter-server<3,>=1.8 in /usr/local/lib/python3.11/dist-packages (from notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.16.0)\n", "Requirement already satisfied: cffi>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.17.1)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.11/dist-packages (from beautifulsoup4->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2.7)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.11/dist-packages (from cffi>=1.0.1->argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2.22)\n", "Requirement already satisfied: websocket-client in /usr/local/lib/python3.11/dist-packages (from jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.8.0)\n", "Downloading groq-0.30.0-py3-none-any.whl (131 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m2.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jedi-0.19.2-py2.py3-none-any.whl (1.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m14.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: jedi, groq\n", "Successfully installed groq-0.30.0 jedi-0.19.2\n"]}]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ac9ea762", "outputId": "7d50aaf1-b78a-46c5-b718-37f63de9d08f"}, "source": ["import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "from groq import Groq\n", "import os\n", "from google.colab import userdata, files\n", "\n", "# Initialize Groq client using Colab secrets\n", "api_key = userdata.get('GROQ_API_KEY')\n", "client = Groq(api_key=api_key)\n", "print(\"API key successfully loaded from Colab secrets\")"], "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["API key successfully loaded from Colab secrets\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "f8ef1137"}, "source": ["## Upload Articles\n", "\n", "Now, upload the three text files containing your research articles."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 212}, "id": "966f71ac", "outputId": "1e5ca821-3b6b-492f-d3b8-90e306d5a12c"}, "source": ["# Upload files widget\n", "print(\"Please upload exactly 3 text files containing research articles:\")\n", "uploaded = files.upload()\n", "\n", "# Store file contents and filenames\n", "file_contents = []\n", "filenames = []\n", "\n", "if len(uploaded) != 3:\n", "    print(\"Please upload exactly 3 files\")\n", "else:\n", "    for filename, content in uploaded.items():\n", "        article_text = content.decode('utf-8')\n", "        file_contents.append(article_text)\n", "        filenames.append(filename)\n", "        print(f\"Loaded: {filename} ({len(article_text.split())} words)\")\n", "\n", "# A simple validation function (no length restriction)\n", "def validate_article_length(article, filename):\n", "    # No length restriction for this example\n", "    return None"], "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Please upload exactly 3 text files containing research articles:\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "     <input type=\"file\" id=\"files-1c642d3d-63de-4fb3-aaa8-b440d6e472ab\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-1c642d3d-63de-4fb3-aaa8-b440d6e472ab\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Saving ChatGPT Search A Guide With Example.txt to ChatGPT Search A Guide With Example.txt\n", "Saving Context Engineering A Guide With Ex.txt to Context Engineering A Guide With Ex.txt\n", "Saving Small Language Models A Guide With.txt to Small Language Models A Guide With.txt\n", "Loaded: ChatGPT Search A Guide With Example.txt (2203 words)\n", "Loaded: Context Engineering A Guide With Ex.txt (2085 words)\n", "Loaded: Small Language Models A Guide With.txt (2457 words)\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "4d1baa8f"}, "source": ["## Define Prompting Functions\n", "\n", "We'll define functions for zero-shot, one-shot, and few-shot prompting based on the desired summary format."]}, {"cell_type": "code", "metadata": {"id": "8a746bf3"}, "source": ["# --- Advanced Prompting Techniques ---\n", "\n", "# --- 1. Chain-of-Thought (CoT) Prompting ---\n", "# This replaces the original zero_shot_prompt\n", "def cot_prompt(article):\n", "    return f\"\"\"\n", "    Please analyze the following research article step by step to create a summary.\n", "\n", "    Article: {article}\n", "\n", "    Think through your summary creation process:\n", "    1. First, identify the background and introduction.\n", "    2. Next, determine the main research objective.\n", "    3. Then, describe the methodology used.\n", "    4. After that, highlight the key results.\n", "    5. Finally, summarize the main takeaways and conclusions.\n", "\n", "    Now, provide the final summary based on this analysis, structured as:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "    \"\"\"\n", "\n", "# --- 2. Tree-of-Thoughts (ToT) Prompting (Simplified Simulation) ---\n", "# This replaces the original one_shot_prompt\n", "def tot_prompt(article, example_article, example_summary):\n", "    # The example is used to prime the model for the ToT structure\n", "    return f\"\"\"\n", "    Consider the research article below. To understand it thoroughly, explore different perspectives or ways to summarize it.\n", "\n", "    Example Article: {example_article}\n", "    Example Summary (for structure reference): {example_summary}\n", "\n", "    Now, for the following article:\n", "    Article: {article}\n", "\n", "    1. Path 1: Focus on the technical methodology and results.\n", "    2. Path 2: Focus on the implications and conclusions for the field.\n", "    3. Path 3: Focus on the background context and research gap addressed.\n", "\n", "    For each path, briefly outline the key points you would include in a summary.\n", "\n", "    Then, synthesize the insights from these paths to create a final, well-rounded summary structured as:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "    \"\"\"\n", "\n", "# --- 3. Role-Based Prompting ---\n", "def role_based_prompt(article):\n", "    return f\"\"\"\n", "    You are a renowned expert in artificial intelligence research. Your task is to summarize the following technical article for a general audience of researchers new to the field.\n", "\n", "    Article: {article}\n", "\n", "    Provide a clear and concise summary structured as:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "    \"\"\"\n", "\n", "# --- 4. ReAct Prompting (Reasoning + Action Simulation) ---\n", "def react_prompt(article):\n", "    return f\"\"\"\n", "    You are tasked with summarizing a research article. Use a ReAct (Reasoning + Action) approach.\n", "\n", "    Article: {article}\n", "\n", "    Thought 1: I need to understand the core components of the article.\n", "    Action 1: Identify the background/introduction section.\n", "    Observation 1: [The model identifies the background/introduction]\n", "    Thought 2: Now I need to find the research objective.\n", "    Action 2: Locate the statement of the research goal.\n", "    Observation 2: [The model identifies the research objective]\n", "    Thought 3: Next, I need details on how the research was conducted.\n", "    Action 3: Extract information about the methodology.\n", "    Observation 3: [The model identifies the methodology]\n", "    Thought 4: What were the main findings?\n", "    Action 4: Find the key results presented.\n", "    Observation 4: [The model identifies the key results]\n", "    Thought 5: Finally, what are the conclusions and implications?\n", "    Action 5: Summarize the main takeaways/conclusions.\n", "    Observation 5: [The model identifies the main takeaways/conclusions]\n", "\n", "    Based on these thoughts and observations, provide the final summary structured as:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "    \"\"\"\n", "\n", "# --- 5. Directional Stimulus Prompting ---\n", "def directional_stimulus_prompt(article):\n", "    return f\"\"\"\n", "    Focus Stimulus: Summarize concisely.\n", "    Detail Stimulus: Include all key technical terms.\n", "    Perspective Stimulus: From the author's viewpoint.\n", "    Article: {article}\n", "\n", "    Based on the stimuli above, create a summary structured as:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "    \"\"\"\n", "\n", "# --- 6. Step-Back Prompting ---\n", "def step_back_prompt(article):\n", "    return f\"\"\"\n", "    Before summarizing the specific article, first answer these general questions:\n", "    - What is the typical structure of a research article?\n", "    - What are the key elements to include in a summary of a research article?\n", "\n", "    Now, applying these general principles, summarize the following article:\n", "\n", "    Article: {article}\n", "\n", "    Provide the summary structured as:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "    \"\"\"\n", "\n", "# --- 7. Zero-Shot (Baseline - Original Structure) ---\n", "# Keep the original zero-shot as one of the options for comparison\n", "def zero_shot_prompt(article):\n", "    return f\"\"\"\n", "    Summarize the following research article in a single paragraph of 150 words or less. Include the following sections explicitly:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "\n", "    Article: {article}\n", "    \"\"\"\n", "\n", "# --- 8. One-Shot (Baseline - Original Structure) ---\n", "# Keep the original one-shot as one of the options for comparison\n", "def one_shot_prompt(article, example_article, example_summary):\n", "    return f\"\"\"\n", "    Summarize the following research article in a single paragraph of 150 words or less. Include the following sections explicitly:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "\n", "    Example Article: {example_article}\n", "    Example Summary: {example_summary}\n", "\n", "    Now summarize this article, including the explicit headings for each section:\n", "    {article}\n", "    \"\"\"\n", "\n", "# --- 9. Few-Shot (Baseline - Original Structure) ---\n", "# Keep the original few-shot as one of the options for comparison\n", "def few_shot_prompt(article, examples):\n", "    prompt = \"\"\"\n", "    Summarize the following research article in a single paragraph of 150 words or less. Include the following sections explicitly:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "    \"\"\"\n", "    for i, (ex_article, ex_summary) in enumerate(examples):\n", "        prompt += f\"\\nExample {i+1}:\\nArticle: {ex_article}\\nSummary: {ex_summary}\\n\"\n", "    prompt += f\"\\nNow summarize this article, including the explicit headings for each section:\\n{article}\"\n", "    return prompt\n"], "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "38324d83"}, "source": ["## Define API Call Function\n", "\n", "This function sends the prompt to the Groq API and returns the generated summary. We will use the `meta-llama/llama-4-scout-17b-16e-instruct` model as requested."]}, {"cell_type": "code", "metadata": {"id": "288fa0fe"}, "source": ["# Function to call Groq API\n", "def get_summary(prompt):\n", "    completion = client.chat.completions.create(\n", "        model=\"meta-llama/llama-4-scout-17b-16e-instruct\", # Using the requested model\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        temperature=0.7,\n", "        max_completion_tokens=200, # Keep max tokens for summary consistent\n", "        top_p=1,\n", "        stream=False,\n", "        stop=None,\n", "    )\n", "    return completion.choices[0].message.content.strip()\n", "\n", "# Function to validate word count of the summary\n", "def validate_summary(summary):\n", "    word_count = len(summary.split())\n", "    if word_count > 150:\n", "        return f\"Word count: {word_count}\"\n", "    else:\n", "        return f\"Word count: {word_count}\""], "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "dd275c5c"}, "source": ["## Process Articles Sequentially\n", "\n", "This section sets up the UI and the logic to process the uploaded articles sequentially using the defined prompting techniques and save the output."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["d9bc2ae124bf4bb4acf1c809baf60d5a", "82080a8e824e464ca0694e5be4148d6a", "b4a7fafd92274e17842d9fa89a844a88", "7fd35c9647b44f62a348ecaa73efeb98", "4945444a79d74b0db1d87bf79e44bdcd", "d9d67bcb9a8a49bab351b96fda1de13f", "82aabf90fb2f400da4f38f6f1a6ec4fd"]}, "id": "48de1b78", "outputId": "983b8bd5-ceae-4463-c6a0-7e26a717e61e"}, "source": ["# --- Cell 5: Process Articles with Advanced Prompting Techniques ---\n", "\n", "# Create UI widgets\n", "process_button = widgets.Button(\n", "    description='Process Articles with Advanced Prompts',\n", "    button_style='success', # 'success', 'info', 'warning', 'danger' or ''\n", "    tooltip='Process all articles using various advanced prompting techniques'\n", ")\n", "\n", "output_area = widgets.Output()\n", "\n", "# Display UI\n", "display(widgets.VBox([process_button, output_area]))\n", "\n", "# Button click handler for processing and saving\n", "def on_process_clicked(b):\n", "    global file_contents, filenames # Ensure we access the variables from earlier cells\n", "\n", "    if len(file_contents) != 3:\n", "        with output_area:\n", "            clear_output()\n", "            print(\"Please upload exactly 3 files first\")\n", "        return\n", "\n", "    # Simple validation (though the original had no length check)\n", "    validation_errors = []\n", "    for i, (article, filename) in enumerate(zip(file_contents, filenames)):\n", "        error = validate_article_length(article, filename)\n", "        if error:\n", "            validation_errors.append(f\"File {filename}: {error}\")\n", "\n", "    if validation_errors:\n", "        with output_area:\n", "            clear_output()\n", "            print(\"Article validation errors:\")\n", "            for error in validation_errors:\n", "                print(error)\n", "        return\n", "\n", "    output_text = \"\"\n", "    with output_area:\n", "        clear_output()\n", "        print(\"Starting processing of articles with advanced prompting techniques...\\n\")\n", "\n", "    # Store summaries generated, potentially for use in techniques that need examples\n", "    # Key: (article_index, technique_name) -> summary\n", "    all_summaries = {}\n", "    # Also store summaries in a list per article for techniques like Few-Shot that need multiple examples\n", "    # Index corresponds to article index\n", "    article_summaries_list = [[] for _ in range(len(file_contents))]\n", "\n", "    # --- Define the techniques to apply ---\n", "    # Format: (Display Name, Function, Requires Example (bool), Requires Multiple Examples (bool))\n", "    techniques = [\n", "        (\"Zero-Shot (Baseline)\", zero_shot_prompt, False, False),\n", "        (\"Chain-of-Thought\", cot_prompt, False, False),\n", "        (\"Role-Based\", role_based_prompt, False, False),\n", "        (\"Directional Stimulus\", directional_stimulus_prompt, False, False),\n", "        (\"Step-Back Abstraction\", step_back_prompt, False, False),\n", "        (\"ReAct\", react_prompt, False, False),\n", "        (\"One-Shot (Baseline)\", one_shot_prompt, True, False), # Requires 1 example\n", "        (\"Tree-of-Thoughts (Simplified)\", tot_prompt, True, False), # Requires 1 example\n", "        (\"Few-Shot (Baseline)\", few_shot_prompt, False, True), # Requires multiple examples\n", "    ]\n", "\n", "    # --- Process each article ---\n", "    for i in range(len(file_contents)):\n", "        article_content = file_contents[i]\n", "        article_filename = filenames[i]\n", "\n", "        output_text += \"=\"*80 + \"\\n\"\n", "        output_text += f\"Processing Article {i+1}: {article_filename}\\n\"\n", "        output_text += \"=\"*80 + \"\\n\"\n", "        with output_area:\n", "            print(\"=\"*80)\n", "            print(f\"Processing Article {i+1}: {article_filename}\")\n", "            print(\"=\"*80)\n", "\n", "        # --- Apply each technique to the current article ---\n", "        for tech_name, prompt_func, needs_example, needs_multiple_examples in techniques:\n", "            output_text += \"-\"*40 + f\"\\nTechnique: {tech_name}\\n\" + \"-\"*40 + \"\\n\"\n", "            with output_area:\n", "                 print(\"-\" * 40)\n", "                 print(f\"Technique: {tech_name}\")\n", "                 print(\"-\" * 40)\n", "\n", "            prompt = \"\"\n", "            try:\n", "                if needs_multiple_examples:\n", "                    # --- Few-Shot (Baseline) ---\n", "                    examples_for_few_shot = []\n", "                    # Use summaries from previous articles (Articles 0 up to i-1)\n", "                    for j in range(i):\n", "                         # Use the Zero-Shot summary of previous articles as examples\n", "                         key = (j, \"Zero-Shot (Baseline)\")\n", "                         if key in all_summaries:\n", "                              examples_for_few_shot.append((file_contents[j], all_summaries[key]))\n", "                         # If Zero-Shot not available, try any summary from that article\n", "                         # elif article_summaries_list[j]:\n", "                         #      examples_for_few_shot.append((file_contents[j], article_summaries_list[j][0]))\n", "\n", "                    if len(examples_for_few_shot) >= 2: # Ensure we have enough examples\n", "                        prompt = prompt_func(article_content, examples_for_few_shot)\n", "                        # print(f\"DEBUG: Using {len(examples_for_few_shot)} examples for Few-Shot.\")\n", "                    elif len(examples_for_few_shot) == 1:\n", "                        # Fallback to One-Shot if only one example\n", "                        prompt = one_shot_prompt(article_content, examples_for_few_shot[0][0], examples_for_few_shot[0][1])\n", "                        tech_name = f\"{tech_name} (Fallback to One-Shot)\"\n", "                        output_text += \"[Info: Only 1 example available, falling back to One-Shot structure]\\n\"\n", "                        with output_area:\n", "                            print(\"[Info: Only 1 example available, falling back to One-Shot structure]\")\n", "                    else:\n", "                        # Fallback to Zero-Shot if no examples\n", "                        prompt = zero_shot_prompt(article_content)\n", "                        tech_name = f\"{tech_name} (Fallback to Zero-Shot)\"\n", "                        output_text += \"[Warning: No examples available, falling back to Zero-Shot]\\n\"\n", "                        with output_area:\n", "                            print(\"[Warning: No examples available, falling back to Zero-Shot]\")\n", "\n", "                elif needs_example:\n", "                     # --- Techniques needing a single example (One-Shot, ToT) ---\n", "                     if i > 0: # Need a previous article\n", "                         # Use the Zero-Shot summary of the immediately preceding article\n", "                         example_key = (i-1, \"Zero-Shot (Baseline)\")\n", "                         if example_key in all_summaries:\n", "                             example_article = file_contents[i-1]\n", "                             example_summary = all_summaries[example_key]\n", "                             prompt = prompt_func(article_content, example_article, example_summary)\n", "                         else:\n", "                             # Fallback if Zero-Shot summary of previous article isn't available\n", "                             prompt = zero_shot_prompt(article_content)\n", "                             tech_name = f\"{tech_name} (Fallback to Zero-Shot - No Example Summary)\"\n", "                             output_text += \"[Warning: Example summary not found, falling back to Zero-Shot]\\n\"\n", "                             with output_area:\n", "                                 print(\"[Warning: Example summary not found, falling back to Zero-Shot]\")\n", "                     else:\n", "                         # Fallback to Zero-Shot for the first article if technique needs an example\n", "                         prompt = zero_shot_prompt(article_content)\n", "                         tech_name = f\"{tech_name} (Fallback to Zero-Shot - First Article)\"\n", "                         output_text += \"[Info: First article, no example available, falling back to Zero-Shot]\\n\"\n", "                         with output_area:\n", "                            print(\"[Info: First article, no example available, falling back to Zero-Shot]\")\n", "\n", "                else:\n", "                    # --- Techniques needing only the article content (Zero-Shot, CoT, Role-Based, etc.) ---\n", "                    prompt = prompt_func(article_content)\n", "\n", "                # --- Get <PERSON><PERSON><PERSON> from LLM ---\n", "                summary = get_summary(prompt)\n", "\n", "                # --- Store Summary ---\n", "                summary_key = (i, tech_name)\n", "                all_summaries[summary_key] = summary\n", "                article_summaries_list[i].append(summary) # Add to list for this article\n", "\n", "                # --- Output Results ---\n", "                output_text += f\"{tech_name} Summary:\\n{summary}\\n\"\n", "                output_text += f\"{validate_summary(summary)}\\n\\n\"\n", "\n", "                with output_area:\n", "                    print(f\"{tech_name} Summary:\")\n", "                    print(summary)\n", "                    print(validate_summary(summary))\n", "                    print(\"\\n\")\n", "\n", "            except Exception as e:\n", "                error_msg = f\"Error processing Article {i+1} with {tech_name}: {e}\"\n", "                output_text += f\"{error_msg}\\n\\n\"\n", "                with output_area:\n", "                    print(error_msg)\n", "                    print(\"\\n\")\n", "\n", "    # --- Final Summary Section ---\n", "    output_text += \"=\"*80 + \"\\n\"\n", "    output_text += \"FINAL SUMMARY OF ALL APPROACHES FOR EACH ARTICLE\\n\"\n", "    output_text += \"=\"*80 + \"\\n\"\n", "    with output_area:\n", "        print(\"=\"*80)\n", "        print(\"FINAL SUMMARY OF ALL APPROACHES FOR EACH ARTICLE\")\n", "        print(\"=\"*80)\n", "\n", "    for i in range(len(file_contents)):\n", "        output_text += f\"\\n--- Article {i+1}: {filenames[i]} ---\\n\"\n", "        output_text += f\"Original Word Count: {len(file_contents[i].split())}\\n\"\n", "        with output_area:\n", "            print(f\"\\n--- Article {i+1}: {filenames[i]} ---\")\n", "            print(f\"Original Word Count: {len(file_contents[i].split())}\")\n", "\n", "        # List summaries generated for this article\n", "        for key, summary in all_summaries.items():\n", "            article_idx, tech_name = key\n", "            if article_idx == i:\n", "                summary_word_count = len(summary.split())\n", "                output_text += f\"  - {tech_name}: Summary Word Count: {summary_word_count}\\n\"\n", "                with output_area:\n", "                     print(f\"  - {tech_name}: Summary Word Count: {summary_word_count}\")\n", "\n", "    # --- Save output to a text file ---\n", "    output_filename = \"advanced_prompting_summaries.txt\"\n", "    try:\n", "        with open(output_filename, \"w\", encoding='utf-8') as f: # Specify encoding\n", "            f.write(output_text)\n", "        with output_area:\n", "            print(f\"\\n✅ Output successfully saved to '{output_filename}'\")\n", "    except Exception as e:\n", "        with output_area:\n", "            print(f\"\\n❌ Error saving output to file: {e}\")\n", "\n", "# Attach event handler\n", "process_button.on_click(on_process_clicked)\n"], "execution_count": 6, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["VBox(children=(But<PERSON>(button_style='success', description='Process Articles with Advanced Prompts', style=Butt…"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d9bc2ae124bf4bb4acf1c809baf60d5a"}}, "metadata": {}}]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"d9bc2ae124bf4bb4acf1c809baf60d5a": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_82080a8e824e464ca0694e5be4148d6a", "IPY_MODEL_b4a7fafd92274e17842d9fa89a844a88"], "layout": "IPY_MODEL_7fd35c9647b44f62a348ecaa73efeb98"}}, "82080a8e824e464ca0694e5be4148d6a": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ButtonView", "button_style": "success", "description": "Process Articles with Advanced Prompts", "disabled": false, "icon": "", "layout": "IPY_MODEL_4945444a79d74b0db1d87bf79e44bdcd", "style": "IPY_MODEL_d9d67bcb9a8a49bab351b96fda1de13f", "tooltip": "Process all articles using various advanced prompting techniques"}}, "b4a7fafd92274e17842d9fa89a844a88": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_82aabf90fb2f400da4f38f6f1a6ec4fd", "msg_id": "", "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Starting processing of articles with advanced prompting techniques...\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["================================================================================\n", "Processing Article 1: ChatGPT Search A Guide With Example.txt\n", "================================================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Zero-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Zero-Shot (Baseline) Summary:\n", "Here is a summary of the research article in 150 words or less, including the specified sections:\n", "\n", "**Background/Introduction**: OpenAI has introduced ChatGPT Search, a new feature that integrates search capabilities into the ChatGPT interface. This feature aims to provide users with direct responses to their queries, rather than just links to websites.\n", "\n", "**Research Objective**: The objective of this article is to provide a comprehensive guide on how to access and use ChatGPT Search, including its features, capabilities, and limitations.\n", "\n", "**Methodology**: The article provides a detailed exploration of ChatGPT Search, including its eligibility criteria, access methods, and query types (informational, navigational, commercial, and transactional).\n", "\n", "**Key Results**: ChatGPT Search provides users with direct responses to their queries, including source citations, and offers features such as real-time information, visually enriched results, and partnerships with major news and data providers.\n", "\n", "**Main Takeaways/Conclusions**: ChatGPT Search has the potential to provide users with accurate and\n", "Word count: 149\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Chain-of-Thought\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Chain-of-Thought Summary:\n", "## Background/Introduction\n", "The article provides a comprehensive guide to ChatGPT Search, a new feature from OpenAI that integrates search capabilities into the ChatGPT interface. ChatGPT Search is currently available to specific groups of users, including ChatGPT Plus subscribers, ChatGPT Team users, and SearchGPT waitlist users, with plans to expand access to ChatGPT Enterprise, Edu users, and eventually free users. The feature aims to provide users with direct, detailed answers to their queries by leveraging a combination of its language model and real-time web search.\n", "\n", "## Research Objective\n", "The primary objective of the article is to explore and explain the functionalities of ChatGPT Search, including how to access it, how to use it for different types of queries (informational, navigational, commercial, and transactional), and the features that make it useful for users. The article also aims to provide insights into how ChatGPT Search works behind the scenes.\n", "\n", "## Methodology\n", "The methodology involves a hands-on exploration of ChatGPT Search. The\n", "Word count: 158\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Role-Based\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Role-Based Summary:\n", "## Background/Introduction\n", "\n", "The article discusses ChatGPT Search, a new feature from OpenAI that integrates search capabilities into the popular ChatGPT interface. This feature allows users to access information from the web directly through ChatGPT, enhancing its ability to provide accurate and up-to-date answers. ChatGPT Search is currently available to ChatGPT Plus subscribers, Team users, and those on the SearchGPT waitlist, with plans for broader availability.\n", "\n", "## Research Objective\n", "\n", "The objective of the article is to provide a comprehensive guide on how to use ChatGPT Search, its features, and how it handles different types of search queries. The article aims to explore the capabilities and limitations of ChatGPT Search, comparing it to traditional search engines.\n", "\n", "## Methodology\n", "\n", "The article provides a hands-on exploration of ChatGPT Search, testing its features and responses to various search queries. The author uses different query types, including informational, navigational, commercial, and transactional queries, to evaluate ChatGPT Search's performance. The article also examines the\n", "Word count: 157\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Directional Stimulus\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Directional Stimulus Summary:\n", "## Background/Introduction\n", "The article provides a comprehensive guide to ChatGPT Search, a new feature from OpenAI that integrates search capabilities into the ChatGPT interface. This feature is designed to provide users with direct answers to their queries by accessing a wide range of web information.\n", "\n", "## Research Objective\n", "The objective of the article is to explore and explain the functionalities of ChatGPT Search, including how to access it, its features, and how it handles different types of search queries.\n", "\n", "## Methodology\n", "The author accessed ChatGPT Search through various platforms (web, desktop app, mobile app, and Chrome extension) and tested it with different types of queries: informational, navigational, commercial, and transactional. The author also analyzed the features of ChatGPT Search, including its partnerships with news and data providers, visual designs, and the technology behind it.\n", "\n", "## Key Results\n", "- **Accessibility**: ChatGPT Search is available to ChatGPT Plus subscribers, ChatGPT Team users, and SearchGPT waitlist users, with plans\n", "Word count: 156\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Step-Back Abstraction\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Step-Back Abstraction Summary:\n", "## General Questions\n", "\n", "### 1. What is the typical structure of a research article?\n", "\n", "The typical structure of a research article includes:\n", "\n", "- **Background/Introduction**: Provides context and background information on the topic, states the research problem, and outlines the objectives of the study.\n", "- **Literature Review**: Reviews existing research relevant to the study to establish a foundation for the current research.\n", "- **Methodology**: Describes the research design, methods, and procedures used to collect and analyze data.\n", "- **Results**: Presents the findings of the study, often including data visualizations and statistical analyses.\n", "- **Discussion**: Interprets the results, relates them to the broader context, and discusses implications.\n", "- **Conclusion**: Summarizes the key findings, restates the importance of the study, and suggests areas for future research.\n", "\n", "### 2. What are the key elements to include in a summary of a research article?\n", "\n", "When summarizing a research article, key elements to include are:\n", "\n", "- **Research Objective\n", "Word count: 152\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: ReAct\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["ReAct Summary:\n", "## Background/Introduction\n", "The article provides a comprehensive guide to ChatGPT Search, a tool developed by OpenAI that integrates search capabilities into the ChatGPT interface. ChatGPT Search is designed to provide users with direct responses to their queries, leveraging a combination of its language model and real-time web search. The tool is currently available to ChatGPT Plus subscribers, ChatGPT Team users, and those on the SearchGPT waitlist, with plans for broader availability.\n", "\n", "## Research Objective\n", "The primary objective of the article is to explore the features, functionalities, and potential applications of ChatGPT Search. This includes understanding how to access the tool, how it handles different types of search queries (informational, navigational, commercial, and transactional), and its underlying technology.\n", "\n", "## Methodology\n", "The article takes a hands-on approach, with the author exploring ChatGPT Search across various platforms (web, desktop app, mobile app, and Chrome extension). The methodology involves testing the tool with different types of queries to assess its performance\n", "Word count: 157\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: One-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["[Info: First article, no example available, falling back to Zero-Shot]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["One-Shot (Baseline) (Fallback to Zero-Shot - First Article) Summary:\n", "Here is a summary of the research article in 150 words or less, including the specified sections:\n", "\n", "**Background/Introduction**: The article explores ChatGPT Search, a new feature from OpenAI that integrates search capabilities into the ChatGPT interface. \n", "**Research Objective**: The objective is to provide a guide on how to access and use ChatGPT Search, its features, and how it handles different types of search queries. \n", "**Methodology**: The article provides a thorough exploration of ChatGPT Search, including its eligibility, accessibility, and features. The author tests ChatGPT Search with various queries, including informational, navigational, commercial, and transactional queries. \n", "**Key Results**: ChatGPT Search provides detailed answers with source citations, handles different query types, and offers features like real-time information and visually enriched results through partnerships with news and data providers. \n", "**Main Takeaways/Conclusions**: ChatGPT Search is a useful tool for finding information and answering questions, but it has areas for improvement, such\n", "Word count: 147\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Tree-of-Thoughts (Simplified)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["[Info: First article, no example available, falling back to Zero-Shot]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Tree-of-Thoughts (Simplified) (Fallback to Zero-Shot - First Article) Summary:\n", "Here is a summary of the article in 150 words or less:\n", "\n", "**Background/Introduction**: OpenAI has introduced ChatGPT Search, a feature that integrates search capabilities into the ChatGPT interface. \n", "\n", "**Research Objective**: The objective is to explore and guide users on how to access and use ChatGPT Search, its features, and how it handles different types of search queries.\n", "\n", "**Methodology**: The article provides a thorough guide on accessing ChatGPT Search across various platforms, including the website, desktop app, mobile app, and Chrome extension. It also examines ChatGPT Search's responses to different types of search queries: informational, navigational, commercial, and transactional.\n", "\n", "**Key Results**: ChatGPT Search provides detailed answers with source citations, handles various query types, and offers features like real-time information and visually enriched results through partnerships with news and data providers.\n", "\n", "**Main Takeaways/Conclusions**: ChatGPT Search offers a unique search experience with its ability to provide direct responses, real-time information, and\n", "Word count: 148\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Few-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["[Warning: No examples available, falling back to Zero-Shot]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Few-Shot (<PERSON><PERSON>) (Fallback to Zero-Shot) Summary:\n", "Here is a summary of the research article in a single paragraph of 150 words or less, including the requested sections:\n", "\n", "**Background/Introduction**: The article explores ChatGPT Search, a new feature from OpenAI that integrates search capabilities into the ChatGPT interface. \n", "**Research Objective**: The objective is to provide a guide on how to access and use ChatGPT Search, its features, and how it handles different types of search queries. \n", "**Methodology**: The article provides an in-depth analysis of ChatGPT Search's responses to various search queries, including informational, navigational, commercial, and transactional queries. \n", "**Key Results**: ChatGPT Search provides detailed answers with source citations, handles different query types, and offers features like real-time information and visually enriched results through partnerships with news and data providers. \n", "**Main Takeaways/Conclusions**: ChatGPT Search is a powerful tool that offers a unique search experience, but it still has areas for improvement, such as providing a reliability score for sources\n", "Word count: 150\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["================================================================================\n", "Processing Article 2: Context Engineering A Guide With Ex.txt\n", "================================================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Zero-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Zero-Shot (Baseline) Summary:\n", "Here is a summary of the research article in a single paragraph of 150 words or less, including the specified sections:\n", "\n", "**Background/Introduction**: The article discusses the limitations of prompt engineering in complex AI applications, where chatbots and AI assistants forget important instructions or lose track of context. **Research Objective**: The objective is to introduce context engineering, a new approach to designing AI systems that manage information flow over time. **Methodology**: The article explains the principles of context engineering, which involves gathering relevant details from multiple sources, organizing them within the model's context window, and managing different types of information. **Key Results**: Context engineering enables AI systems to feel more intelligent and aware by referencing previous conversations, accessing user data, and understanding communication styles. **Main Takeaways/Conclusions**: Context engineering is essential for building complex AI applications, such as conversational AI, document analysis tools, and coding assistants, and techniques like RAG systems, context validation, and tool management can mitigate context failures and improve\n", "Word count: 160\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Chain-of-Thought\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Chain-of-Thought Summary:\n", "## Background/Introduction\n", "\n", "The article discusses the limitations of traditional prompt engineering in complex AI applications. As AI use cases grow more complex, writing a clever prompt is just one small part of a much larger challenge: context engineering. Context engineering is the practice of designing systems that decide what information an AI model sees before it generates a response. This approach requires managing several different types of information that make up the full context, including system instructions, conversation history, retrieved information from documents or databases, available tools and their definitions, structured output formats and schemas, real-time data and external API responses.\n", "\n", "## Research Objective\n", "\n", "The main objective of this article is to explain what context engineering is, how it works, when to use it instead of regular prompt engineering, and the practical techniques that make AI systems smarter and more context-aware.\n", "\n", "## Methodology\n", "\n", "The article provides an in-depth analysis of context engineering, including its definition, principles, and applications. It also discusses the differences between context engineering and prompt engineering, and highlights\n", "Word count: 171\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Role-Based\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Role-Based Summary:\n", "## Background/Introduction\n", "\n", "The article discusses the limitations of traditional prompt engineering in artificial intelligence (AI) and introduces the concept of context engineering. As AI use cases become more complex, writing a clever prompt is no longer sufficient. Context engineering is the practice of designing systems that decide what information an AI model sees before it generates a response. This approach involves gathering relevant details from multiple sources and organizing them within the model's context window.\n", "\n", "## Research Objective\n", "\n", "The objective of the article is to explain the principles and techniques of context engineering, its applications, and its benefits in building more intelligent and context-aware AI systems.\n", "\n", "## Methodology\n", "\n", "The article provides an overview of context engineering, its differences from prompt engineering, and its applications in various AI systems, such as conversational AI, document analysis tools, and coding assistants. The author also discusses the challenges of context engineering, including context poisoning, distraction, confusion, and clash, and presents techniques to mitigate these issues.\n", "\n", "## Key Results\n", "\n", "The article highlights the following key\n", "Word count: 170\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Directional Stimulus\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Directional Stimulus Summary:\n", "## Background/Introduction\n", "The article discusses the limitations of traditional prompt engineering in complex AI applications, where chatbots, code assistants, and RAG tools often struggle to retain crucial information from earlier interactions. This challenge highlights the need for a more comprehensive approach to managing information flow in AI systems.\n", "\n", "## Research Objective\n", "The objective is to introduce and explain context engineering, a practice that involves designing systems to gather relevant information from multiple sources and organize it within an AI model's context window. This enables more intelligent and context-aware AI behavior.\n", "\n", "## Methodology\n", "The article explains the principles and techniques of context engineering, including managing different types of information (system instructions, conversation history, user preferences, retrieved documents, available tools, structured output formats, real-time data, and API responses). It also discusses the challenges of working within context window limitations while maintaining coherent conversations over time.\n", "\n", "## Key Results\n", "The article highlights several key results and techniques in context engineering:\n", "- **RAG Systems**: Retrieval-augmented generation systems that\n", "Word count: 164\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Step-Back Abstraction\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Step-Back Abstraction Summary:\n", "## General Questions\n", "\n", "### 1. What is the typical structure of a research article?\n", "\n", "The typical structure of a research article includes:\n", "\n", "- **Background/Introduction**: Provides context, states the problem, and outlines the research objective.\n", "- **Literature Review**: Discusses relevant previous research and the current state of knowledge in the field.\n", "- **Research Objective/Hypothesis**: Clearly states the aim of the study and its hypothesis.\n", "- **Methodology**: Describes the research design, methods, and procedures used to collect and analyze data.\n", "- **Results**: Presents the findings of the study, often including data visualizations and statistical analyses.\n", "- **Discussion**: Interprets the results, discusses their implications, and relates them to the broader literature.\n", "- **Conclusion**: Summarizes the main findings, highlights their significance, and suggests future research directions.\n", "\n", "### 2. What are the key elements to include in a summary of a research article?\n", "\n", "When summarizing a research article, key elements to include are\n", "Word count: 148\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: ReAct\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["ReAct Summary:\n", "## Background/Introduction\n", "\n", "The article discusses the concept of context engineering, a crucial aspect of developing advanced AI systems. As AI use cases become more complex, simply writing clever prompts is insufficient. Context engineering involves designing systems that decide what information an AI model sees before generating a response. This approach is essential for managing the flow of information in and out of AI systems, especially in applications that require maintaining coherent conversations over time.\n", "\n", "## Research Objective\n", "\n", "The research objective is to explain the concept of context engineering, its principles, and its applications. The article aims to provide a comprehensive guide on context engineering, including its differences from traditional prompt engineering, practical techniques, and best practices for implementation.\n", "\n", "## Methodology\n", "\n", "The article provides an in-depth analysis of context engineering, including its definition, principles, and techniques. It discusses various approaches, such as Retrieval Augmented Generation (RAG) systems, AI agents, and context validation. The methodology involves examining the limitations of traditional prompt engineering and highlighting the benefits of context engineering in\n", "Word count: 169\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: One-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["One-Shot (Baseline) Summary:\n", "Here is a summary of the research article in 150 words or less, including the specified sections:\n", "\n", "**Background/Introduction**: The article discusses the limitations of traditional prompt engineering in complex AI applications, where chatbots and AI assistants need to maintain context over multiple interactions.\n", "\n", "**Research Objective**: The objective is to introduce context engineering, a new approach that focuses on designing systems that manage information flow to AI models, enabling them to maintain relevant context over time.\n", "\n", "**Methodology**: The article explains the principles of context engineering, including managing different types of information, such as conversation history, user data, and external documents, and organizing them within the model's context window.\n", "\n", "**Key Results**: Context engineering enables AI systems to feel more intelligent and aware, referencing previous conversations, accessing user data, and understanding communication styles. Techniques like RAG systems, context validation, and tool management mitigate context failures.\n", "\n", "**Main Takeaways/Conclusions**: Context engineering is essential for building complex AI applications, such as conversational AI,\n", "Word count: 157\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Tree-of-Thoughts (Simplified)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Tree-of-Thoughts (Simplified) Summary:\n", "## Path 1: Focus on the technical methodology and results.\n", "\n", "### Key Points:\n", "\n", "*   **Context Engineering Definition**: Context engineering is the practice of designing systems that decide what information an AI model sees before it generates a response.\n", "*   **Context Engineering Techniques**: The article discusses various techniques such as Retrieval Augmented Generation (RAG) systems, AI agents, and context validation.\n", "*   **Context Failures and Mitigation Techniques**: The article identifies four context failures (context poisoning, context distraction, context confusion, and context clash) and provides mitigation techniques such as context validation, context summarization, tool loadout management, and context pruning.\n", "\n", "## Path 2: Focus on the implications and conclusions for the field.\n", "\n", "### Key Points:\n", "\n", "*   **Shift from Prompt Engineering to Context Engineering**: The article highlights the importance of context engineering over traditional prompt engineering for complex AI applications.\n", "*   **Impact on AI Development**: Context engineering represents the next phase of AI development, focusing on managing information flow over\n", "Word count: 155\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Few-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["[Info: Only 1 example available, falling back to One-Shot structure]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Few-Shot (<PERSON>ine) (Fallback to One-Shot) Summary:\n", "Here is a summary of the article in 150 words or less, including the specified sections:\n", "\n", "**Background/Introduction**: The article discusses the limitations of traditional prompt engineering in complex AI applications, where chatbots and AI assistants often forget important information or lose track of context.\n", "\n", "**Research Objective**: The objective is to introduce context engineering, a new approach to designing AI systems that manage information flow over time, and to provide practical techniques for building more context-aware AI systems.\n", "\n", "**Methodology**: The article explains the principles of context engineering, including designing systems that gather relevant details from multiple sources, organizing them within the model's context window, and managing different types of information.\n", "\n", "**Key Results**: Context engineering enables AI systems to feel more intelligent and aware, referencing previous conversations, accessing user data, and understanding communication style.\n", "\n", "**Main Takeaways/Conclusions**: Context engineering is essential for building complex AI applications, such as conversational AI, document analysis tools, and coding assistants, and techniques like RAG systems,\n", "Word count: 158\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["================================================================================\n", "Processing Article 3: Small Language Models A Guide With.txt\n", "================================================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Zero-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Zero-Shot (Baseline) Summary:\n", "Here is a summary of the article in 150 words or less:\n", "\n", "**Background/Introduction**: Large language models (LLMs) require significant computational power and resources, making them inaccessible to those with limited resources. Small language models (SLMs) offer a solution, being smaller, faster, and more customizable.\n", "\n", "**Research Objective**: To guide readers on SLMs, their characteristics, applications, and advantages over LLMs.\n", "\n", "**Methodology**: The article reviews existing SLMs, their development, and techniques used to create them, such as distillation, pruning, and quantization.\n", "\n", "**Key Results**: SLMs have fewer parameters (typically under 10 billion), making them more efficient and accessible. They are suitable for specific tasks, such as customer support, healthcare, and education, and can run on devices with limited resources.\n", "\n", "**Main Takeaways/Conclusions**: SLMs offer a balance between performance and resource efficiency, making AI more accessible. They are ideal for on-device AI, edge computing, and applications\n", "Word count: 140\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Chain-of-Thought\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Chain-of-Thought Summary:\n", "## Background/Introduction\n", "\n", "The article discusses the emergence and benefits of Small Language Models (SLMs) as a more accessible and efficient alternative to Large Language Models (LLMs). SLMs are designed to be smaller, faster, and more easily customizable than LLMs, making them suitable for applications with limited resources. The article provides an overview of SLMs, their characteristics, and their applications.\n", "\n", "## Research Objective\n", "\n", "The research objective is to provide a comprehensive guide to Small Language Models, including their definition, characteristics, examples, and applications. The article aims to demonstrate the benefits and potential of SLMs in making AI more accessible and efficient.\n", "\n", "## Methodology\n", "\n", "The article uses a descriptive approach to explain the concept of SLMs, their architecture, and their applications. It provides a timeline of SLM development from 2019 to 2024, highlighting key models and their features. The article also discusses various techniques used to create SLMs, such as distillation, pruning, and quantization.\n", "\n", "## Key Results\n", "Word count: 155\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Role-Based\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Role-Based Summary:\n", "**Background/Introduction**\n", "\n", "The field of artificial intelligence (AI) has witnessed significant advancements in recent years, particularly in the development of language models. Large Language Models (LLMs) like GPT-4o have achieved remarkable success, but they require substantial computational power, memory, and data. This has led to the development of Small Language Models (SLMs), which are designed to be more efficient, accessible, and customizable.\n", "\n", "**Research Objective**\n", "\n", "The objective of this research is to provide an overview of Small Language Models (SLMs), their characteristics, applications, and advantages over Large Language Models (LLMs). The goal is to make AI more accessible and efficient for those with limited resources.\n", "\n", "**Methodology**\n", "\n", "The article provides a comprehensive review of SLMs, including their definition, key characteristics, examples, and applications. It also discusses the techniques used to create SLMs, such as distillation, pruning, and quantization. The article compares SLMs with LLMs, highlighting their differences in terms of task\n", "Word count: 148\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Directional Stimulus\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Directional Stimulus Summary:\n", "## Background/Introduction\n", "\n", "The article discusses the emergence and benefits of Small Language Models (SLMs) as a more accessible and efficient alternative to Large Language Models (LLMs). SLMs are designed to be smaller, faster, and more easily customizable than LLMs, making them suitable for applications with limited resources.\n", "\n", "## Research Objective\n", "\n", "The objective is to provide an overview of SLMs, their characteristics, examples, and applications, as well as compare them with LLMs.\n", "\n", "## Methodology\n", "\n", "The article reviews existing literature on SLMs, including their development timeline, technical aspects, and use cases.\n", "\n", "## Key Results\n", "\n", "* SLMs have fewer parameters (typically under 10 billion) and require less computational power and data to train.\n", "* They are more efficient, accessible, and customizable than LLMs.\n", "* SLMs are suitable for specific tasks and applications, such as on-device AI, personalized AI, and IoT.\n", "* Examples of SLMs include Qwen2, Mistral Nemo 12B\n", "Word count: 146\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Step-Back Abstraction\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Step-Back Abstraction Summary:\n", "## General Questions\n", "\n", "### 1. What is the typical structure of a research article?\n", "\n", "The typical structure of a research article includes:\n", "\n", "- **Background/Introduction**: Provides context and background information on the research topic, including the problem statement and motivation.\n", "- **Research Objective**: Clearly states the objective or hypothesis of the study.\n", "- **Methodology**: Describes the methods and techniques used to conduct the research.\n", "- **Key Results**: Presents the main findings of the study.\n", "- **Discussion**: Interprets the results, discusses their implications, and relates them to existing knowledge.\n", "- **Conclusion**: Summarizes the main takeaways and conclusions of the study.\n", "\n", "### 2. What are the key elements to include in a summary of a research article?\n", "\n", "When summarizing a research article, key elements to include are:\n", "\n", "- The main research question or objective.\n", "- The methodology used.\n", "- The key results or findings.\n", "- The main conclusions or takeaways.\n", "\n", "## Applying General Principles to Summarize the\n", "Word count: 154\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: ReAct\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["ReAct Summary:\n", "## Background/Introduction\n", "\n", "Small Language Models (SLMs) are designed to make AI more accessible and efficient, especially for those with limited resources. Unlike Large Language Models (LLMs) that have hundreds of billions of parameters, SLMs use far fewer—typically in the millions to a few billion. This reduction in size dramatically decreases computational costs and energy usage, making SLMs more suitable for devices with limited resources, such as smartphones and IoT devices.\n", "\n", "## Research Objective\n", "\n", "The objective of this guide is to provide an overview of SLMs, including their characteristics, examples, how they work, techniques for creating them, and their applications. The goal is to highlight the benefits and potential of SLMs in making AI more accessible and efficient.\n", "\n", "## Methodology\n", "\n", "The article reviews existing literature on SLMs, including their development over time, key characteristics, and various techniques used to create them, such as distillation, pruning, and quantization. It also examines the applications of SLMs across different industries and compares them\n", "Word count: 159\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: One-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["One-Shot (Baseline) Summary:\n", "Here is a summary of the research article in a single paragraph of 150 words or less, including the specified sections:\n", "\n", "**Background/Introduction**: The article discusses the limitations of large language models (LLMs) and introduces small language models (SLMs) as a more efficient and accessible alternative. **Research Objective**: The objective is to explain the concept, characteristics, and applications of SLMs. **Methodology**: The article describes the key features of SLMs, including their smaller size (typically under 10 billion parameters), efficiency, accessibility, and customizability. **Key Results**: SLMs have been developed using techniques like distillation, pruning, and quantization, and have applications in on-device AI, personalized AI, and IoT. **Main Takeaways/Conclusions**: SLMs offer a balance between performance and resource efficiency, making them suitable for specific tasks and domains, and are a better choice than LLMs when resource constraints are a concern or when deploying AI on devices with limited power.\n", "Word count: 145\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Tree-of-Thoughts (Simplified)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Tree-of-Thoughts (Simplified) Summary:\n", "## Path1: Focus on the technical methodology and results.\n", "\n", "**Key Points:**\n", "\n", "1. **Definition and Characteristics**: Small Language Models (SLMs) are defined by their smaller size (typically under 10 billion parameters), efficiency, accessibility, customization, and faster inference times.\n", "2. **Technical Approaches**: SLMs are created using techniques like distillation, pruning, and quantization, which enable them to be smaller, faster, and more efficient.\n", "3. **Examples and Applications**: Various SLMs are listed with their parameters and key features, such as Qwen2, Mistral Nemo 12B, and TinyLlama, highlighting their use in on-device AI, personalized AI, IoT, and other applications.\n", "4. **Performance Balance**: SLMs balance size and performance, being less versatile than LLMs but excelling in specific tasks.\n", "\n", "## Path2: Focus on the implications and conclusions for the field.\n", "\n", "**Key Points:**\n", "\n", "1. **Accessibility and Efficiency**: SLMs make AI more\n", "Word count: 133\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["----------------------------------------\n", "Technique: Few-Shot (Baseline)\n", "----------------------------------------\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Few-Shot (Baseline) Summary:\n", "Here is a summary of the research article in a single paragraph of 150 words or less, including the specified sections:\n", "\n", "**Background/Introduction**: Small language models (SLMs) are compact, efficient versions of large language models (LLMs), with fewer parameters (typically under 10 billion) that reduce computational costs and energy usage. **Research Objective**: The objective is to introduce SLMs, their characteristics, and applications, and to compare them with LLMs. **Methodology**: The article explains the key features of SLMs, such as efficiency, accessibility, customization, faster inference, and examples of SLMs, including Qwen2, Mistral Nemo12B, and Llama3.1.8B. **Key Results**: SLMs are suitable for specific tasks, such as customer support, healthcare, and education, and can be used on-device, offline, or in areas with limited connectivity. **Main Takeaways/Conclusions**: SLMs offer a balance between performance and\n", "Word count: 129\n", "\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["================================================================================\n", "FINAL SUMMARY OF ALL APPROACHES FOR EACH ARTICLE\n", "================================================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "--- Article 1: ChatGPT Search A Guide With Example.txt ---\n", "Original Word Count: 2203\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Zero-Shot (Baseline): Summary Word Count: 149\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Chain-of-Thought: Summary Word Count: 158\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Role-Based: Summary Word Count: 157\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Directional Stimulus: Summary Word Count: 156\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Step-Back Abstraction: Summary Word Count: 152\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - ReAct: Summary Word Count: 157\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - One-Shot (Baseline) (Fallback to Zero-Shot - First Article): Summary Word Count: 147\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Tree-of-Thoughts (Simplified) (Fallback to Zero-Shot - First Article): Summary Word Count: 148\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Few-Shot (Baseline) (Fallback to Zero-Shot): Summary Word Count: 150\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "--- Article 2: Context Engineering A Guide With Ex.txt ---\n", "Original Word Count: 2085\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Zero-Shot (Baseline): Summary Word Count: 160\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Chain-of-Thought: Summary Word Count: 171\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Role-Based: Summary Word Count: 170\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Directional Stimulus: Summary Word Count: 164\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Step-Back Abstraction: Summary Word Count: 148\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - ReAct: Summary Word Count: 169\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - One-Shot (Baseline): Summary Word Count: 157\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Tree-of-Thoughts (Simplified): Summary Word Count: 155\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Few-Shot (Baseline) (Fallback to One-Shot): Summary Word Count: 158\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "--- Article 3: Small Language Models A Guide With.txt ---\n", "Original Word Count: 2457\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Zero-Shot (Baseline): Summary Word Count: 140\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Chain-of-Thought: Summary Word Count: 155\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Role-Based: Summary Word Count: 148\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Directional Stimulus: Summary Word Count: 146\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Step-Back Abstraction: Summary Word Count: 154\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - ReAct: Summary Word Count: 159\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - One-Shot (Baseline): Summary Word Count: 145\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Tree-of-Thoughts (Simplified): Summary Word Count: 133\n"]}, {"output_type": "stream", "name": "stdout", "text": ["  - Few-Shot (Baseline): Summary Word Count: 129\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "✅ Output successfully saved to 'advanced_prompting_summaries.txt'\n"]}]}}, "7fd35c9647b44f62a348ecaa73efeb98": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4945444a79d74b0db1d87bf79e44bdcd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9d67bcb9a8a49bab351b96fda1de13f": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_color": null, "font_weight": ""}}, "82aabf90fb2f400da4f38f6f1a6ec4fd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 0}