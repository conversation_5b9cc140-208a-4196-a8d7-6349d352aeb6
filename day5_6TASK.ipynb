# Install required libraries
!pip install groq langchain ipywidgets



# Import necessary modules
import os
from google.colab import userdata, files
from groq import Groq
from langchain.text_splitter import (
    CharacterTextSplitter,      # For Fixed Size Chunking
    SentenceTransformersTokenTextSplitter, # For Sentence Chunking (Alternative: NLTKSentenceSplitter)
    RecursiveCharacterTextSplitter # For Recursive Chunking
)

# --- Initialize Groq Client ---
# Make sure you have stored your Groq API key in Colab Secrets named 'GROQ_API_KEY'
try:
    api_key = userdata.get('GROQ_API_KEY')
    client = Groq(api_key=api_key)
    print("Groq API key loaded successfully.")
    # You can print the model name if needed, or define it here
    model_name = "meta-llama/llama-4-scout-17b-16e-instruct" # Example from your doc
except Exception as e:
    print(f" Error loading API key: {e}")
    # Handle error or stop execution if key is critical


# Upload your 5-page text document (.txt)
print("Please upload your 5-page text document (.txt):")
uploaded = files.upload()

# Process the uploaded file
document_text = ""
filename = ""
if len(uploaded) == 1:
    filename = list(uploaded.keys())[0]
    document_text = uploaded[filename].decode('utf-8')
    print(f"Loaded document: {filename}")
    print(f"   Approximate words: {len(document_text.split())}")
elif len(uploaded) == 0:
    print("No file uploaded.")
else:
    print("Please upload exactly one file.")

# Check if document was loaded
if not document_text:
    print(" No document text available. Please upload a file and rerun this cell.")
else:
    # Optional: Basic validation or preview
    # print(f"First 500 characters:\n{document_text[:500]}...")
    pass


# --- Define Chunking Parameters ---
# You can adjust these parameters based on your document and requirements

# Parameters for Fixed Size (Character) Chunking
FIXED_CHUNK_SIZE = 1000  # Characters
FIXED_CHUNK_OVERLAP = 100 # Characters

# Parameters for Sentence Chunking (using Token-based approx)
# Note: SentenceTransformersTokenTextSplitter splits by sentences first, then ensures chunks are under token limit.
SENTENCE_CHUNK_SIZE = 80  # Tokens (approx sentence length limit) 50 to 60 chara max
SENTENCE_CHUNK_OVERLAP = 10 # Tokens

# Parameters for Recursive Character Chunking
RECURSIVE_CHUNK_SIZE = 1000  # Characters
RECURSIVE_CHUNK_OVERLAP = 100 # Characters

# --- Initialize Chunking Splitters ---
try:
    fixed_size_splitter = CharacterTextSplitter(
        separator="\n\n",  # Try splitting by paragraphs first
        chunk_size=FIXED_CHUNK_SIZE,
        chunk_overlap=FIXED_CHUNK_OVERLAP,
        length_function=len,
        is_separator_regex=False,
    )
    print("Fixed Size Chunking Splitter initialized.")
except Exception as e:
    print(f" Error initializing Fixed Size Splitter: {e}")
    fixed_size_splitter = None

try:
    # Requires sentence-transformers: !pip install sentence-transformers
    # This splits into sentences, then groups sentences into chunks up to chunk_size tokens.
    sentence_splitter = SentenceTransformersTokenTextSplitter(
        chunk_size=SENTENCE_CHUNK_SIZE,
        chunk_overlap=SENTENCE_CHUNK_OVERLAP
    )
    print(" Sentence Chunking Splitter initialized.")
except Exception as e:
    print(f"Error initializing Sentence Splitter (might need 'sentence-transformers'): {e}")
    print("   Trying alternative NLTK-based sentence splitter...")
    try:
        # Alternative if sentence-transformers is problematic
        from langchain.text_splitter import NLTKTextSplitter
        sentence_splitter = NLTKTextSplitter() # Splits by sentences
        # Wrap it to add chunking if needed
        sentence_splitter = CharacterTextSplitter(
             separator=" ", # Split sentences by space if needed, not ideal but simpler
             chunk_size=SENTENCE_CHUNK_SIZE * 4, # Approx chars, very rough
             chunk_overlap=SENTENCE_CHUNK_OVERLAP * 4,
             length_function=len
        )
        print(" Alternative Sentence Chunking Splitter initialized.")
    except Exception as alt_e:
        print(f"Error initializing alternative Sentence Splitter: {alt_e}")
        sentence_splitter = None


try:
    recursive_splitter = RecursiveCharacterTextSplitter(
        chunk_size=RECURSIVE_CHUNK_SIZE,
        chunk_overlap=RECURSIVE_CHUNK_OVERLAP,
        length_function=len,
        is_separator_regex=False,
        separators=["\n\n", "\n", ". ", " ", ""] # Try these separators in order
    )
    print("Recursive Character Chunking Splitter initialized.")
except Exception as e:
    print(f" Error initializing Recursive Splitter: {e}")
    recursive_splitter = None

# Store splitters for easy access
splitters = {
    "Fixed Size": fixed_size_splitter,
    "Sentence": sentence_splitter,
    "Recursive": recursive_splitter
}


# Check if document is loaded
if not document_text:
    print(" No document text available. Please run the upload cell first.")
else:
    chunked_results = {}

    for strategy_name, splitter in splitters.items():
        print(f"\n--- Applying Chunking Strategy: {strategy_name} ---")
        if splitter is None:
            print(f" Splitter for {strategy_name} not initialized. Skipping.")
            chunked_results[strategy_name] = {'error': 'Splitter not initialized'}
            continue

        try:
            # Perform the chunking
            docs = splitter.create_documents([document_text])

            print(f"Number of chunks created: {len(docs)}")
            # Optional: Print first chunk to inspect
            # if docs:
            #     print(f"Sample Chunk 1 (chars {len(docs[0].page_content)}):\n{docs[0].page_content[:200]}...")

            # Store results
            chunked_results[strategy_name] = {
                'chunks': docs,
                'num_chunks': len(docs)
            }

        except Exception as e:
            print(f" Error applying {strategy_name} chunking: {e}")
            chunked_results[strategy_name] = {'error': str(e)}

    print("\n--- Chunking Process Complete ---")
    # chunked_results now contains the output for each strategy


# --- Import necessary libraries (if not already imported) ---
import re # For basic text search

# --- Define User Query Functions (Text-Based, No Embeddings) ---
# These functions will work directly with the chunked text data.

def query_find_keyword(query_text, chunks_data):
    """
    Query Function 1: Find chunks containing specific keywords.
    Strategy: Search chunk text for the keywords present in the query.
    """
    print(f"--- Query (Keyword Search): {query_text} ---")
    # Extract keywords from the query (simple split, could be improved)
    keywords = query_text.lower().split()
    # Remove common stop words if desired (optional)
    # stop_words = {'find', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can'}
    # keywords = [word for word in keywords if word not in stop_words]

    if not keywords:
        print("No keywords found in query.")
        return

    chunk_texts = [doc.page_content for doc in chunks_data['chunks']]
    if not chunk_texts:
        print("No chunks available for this method.")
        return

    matching_chunks = []
    for i, chunk_text in enumerate(chunk_texts):
        # Simple case-insensitive search for any keyword
        # This could be made more sophisticated (e.g., phrase search, regex)
        found_keywords = [kw for kw in keywords if kw in chunk_text.lower()]
        if found_keywords:
            matching_chunks.append((i, chunk_text, found_keywords))

    if matching_chunks:
        print(f"Found {len(matching_chunks)} chunk(s) containing keywords: {', '.join(keywords)}")
        for idx, chunk_text, found_kws in matching_chunks:
            print(f"\n--- Match in Chunk {idx} (Keywords found: {', '.join(found_kws)}) ---")
            # Print a snippet around the first found keyword for context
            first_kw = found_kws[0]
            # Find the position of the keyword (case-insensitive)
            pos = chunk_text.lower().find(first_kw)
            start = max(0, pos - 100) # Show 100 chars before
            end = min(len(chunk_text), pos + len(first_kw) + 100) # Show 100 chars after
            snippet = chunk_text[start:end]
            # Highlight the keyword in the snippet (optional)
            # snippet = re.sub(f'({re.escape(first_kw)})', r'**\1**', snippet, flags=re.IGNORECASE)
            print(f"...{snippet}...")
    else:
        print(f"No chunks found containing keywords: {', '.join(keywords)}")
    print("-" * 40)

def query_get_chunk_stats(query_text, chunks_data):
    """
    Query Function 2: Get statistics about the chunks.
    Strategy: Calculate number of chunks, average length, etc.
    """
    print(f"--- Query (Chunk Statistics): {query_text} ---")
    try:
        num_chunks = chunks_data.get('num_chunks', len(chunks_data.get('chunks', [])))
        chunks_list = chunks_data.get('chunks', [])
        if not chunks_list:
            print("No chunks available.")
            return

        lengths = [len(doc.page_content) for doc in chunks_list]
        total_chars = sum(lengths)
        avg_chars = total_chars / len(lengths) if lengths else 0
        min_chars = min(lengths) if lengths else 0
        max_chars = max(lengths) if lengths else 0

        print(f"Number of Chunks: {num_chunks}")
        print(f"Total Characters: {total_chars}")
        print(f"Average Chunk Length (chars): {avg_chars:.2f}")
        print(f"Shortest Chunk Length (chars): {min_chars}")
        print(f"Longest Chunk Length (chars): {max_chars}")
        print("-" * 40)
    except Exception as e:
        print(f"Error calculating statistics: {e}")

def query_preview_chunks(query_text, chunks_data):
    """
    Query Function 3: Preview the beginning and end chunks.
    Strategy: Show the first and last few chunks.
    """
    print(f"--- Query (Preview Chunks): {query_text} ---")
    try:
        chunks_list = chunks_data.get('chunks', [])
        if not chunks_list:
            print("No chunks available.")
            return
        num_chunks = len(chunks_list)
        print(f"Total Chunks: {num_chunks}")

        n_preview = min(2, num_chunks) # Preview first and last 2, or less if fewer chunks
        print(f"\n--- First {n_preview} Chunk(s) ---")
        for i in range(n_preview):
            print(f"\n--- Chunk {i} (Length: {len(chunks_list[i].page_content)} chars) ---")
            content = chunks_list[i].page_content
            print(content[:500] + ("..." if len(content) > 500 else ""))

        if num_chunks > n_preview:
            print(f"\n--- Last {n_preview} Chunk(s) ---")
            for i in range(max(n_preview, num_chunks - n_preview), num_chunks):
                 print(f"\n--- Chunk {i} (Length: {len(chunks_list[i].page_content)} chars) ---")
                 content = chunks_list[i].page_content
                 print(content[:500] + ("..." if len(content) > 500 else ""))
        print("-" * 40)
    except Exception as e:
        print(f"Error previewing chunks: {e}")

def query_find_longest_shortest(query_text, chunks_data):
    """
    Query Function 4: Find the longest and shortest chunks.
    Strategy: Iterate through chunks and compare lengths.
    """
    print(f"--- Query (Find Longest/Shortest): {query_text} ---")
    try:
        chunks_list = chunks_data.get('chunks', [])
        if not chunks_list:
            print("No chunks available.")
            return

        lengths = [(i, len(doc.page_content)) for i, doc in enumerate(chunks_list)]
        if not lengths:
             print("No chunk lengths calculated.")
             return

        # Find shortest and longest
        shortest_idx, shortest_len = min(lengths, key=lambda x: x[1])
        longest_idx, longest_len = max(lengths, key=lambda x: x[1])

        print(f"Shortest Chunk: Index {shortest_idx}, Length {shortest_len} chars")
        print(f"Content (first 300 chars): {chunks_list[shortest_idx].page_content[:300]}...")
        print("-" * 20)
        print(f"Longest Chunk: Index {longest_idx}, Length {longest_len} chars")
        print(f"Content (first 300 chars): {chunks_list[longest_idx].page_content[:300]}...")
        print("-" * 40)
    except Exception as e:
        print(f"Error finding longest/shortest chunks: {e}")

def query_simple_summary_by_concat(query_text, chunks_data):
    """
    Query Function 5: Simple summary by concatenating beginnings of chunks.
    Strategy: Take the first N characters from the first M chunks.
    Note: This is a very basic approach, not semantic like LLM summaries.
    """
    print(f"--- Query (Simple Concat Summary): {query_text} ---")
    try:
        chunks_list = chunks_data.get('chunks', [])
        if not chunks_list:
            print("No chunks available.")
            return

        summary_parts = []
        chars_taken_total = 0
        target_summary_length = 1000 # Aim for a summary of ~1000 chars
        chars_per_chunk = 200 # Take first 200 chars from each chunk initially

        for doc in chunks_list:
            content = doc.page_content
            take_chars = min(chars_per_chunk, len(content), target_summary_length - chars_taken_total)
            if take_chars > 0:
                summary_parts.append(content[:take_chars])
                chars_taken_total += take_chars
            if chars_taken_total >= target_summary_length:
                break

        if summary_parts:
            combined_summary = " ... ".join(summary_parts) # Join with separator
            print(f"Combined Summary (approx {chars_taken_total} chars):")
            print(combined_summary)
        else:
            print("Could not generate summary from chunks.")
        print("-" * 40)
    except Exception as e:
        print(f"Error generating simple summary: {e}")

# --- Example Usage of Query Functions ---
# You can run these queries after chunking is complete (Cell 5) and before embedding (Cell 6+)

# Let's run the queries on one of the chunking results, e.g., 'Recursive'
target_method_for_queries = "Recursive" # You can change this to "Fixed Size" or "Sentence"

if target_method_for_queries in chunked_results and 'error' not in chunked_results[target_method_for_queries]:
    chunks_data_for_query = chunked_results[target_method_for_queries]

    # Define your simple text-based queries
    simple_queries_and_functions = [
        ("Find information about clinical trials", query_find_keyword),
        ("Show me statistics about the chunks", query_get_chunk_stats),
        ("Preview the first and last chunks", query_preview_chunks),
        ("Which chunks are the longest and shortest?", query_find_longest_shortest),
        ("Create a simple summary from chunk beginnings", query_simple_summary_by_concat),
    ]

    print(f"\nRunning simple text-based queries using chunks from '{target_method_for_queries}' method:")
    print("="*70)
    # Execute queries
    for query_text, query_func in simple_queries_and_functions:
        query_func(query_text, chunks_data_for_query)
        print("\n" + "="*70 + "\n") # Separator between query results

else:
    print(f"Error: Chunking results for method '{target_method_for_queries}' not found or has errors.")


# Check if chunking was performed
if 'chunked_results' not in locals() or not document_text:
    print("Chunking results not found. Please run the chunking cell first.")
else:
    for strategy_name, result in chunked_results.items():
        print(f"\n--- Inspecting {strategy_name} Chunks ---")
        if 'error' in result:
            print(f"  Error: {result['error']}")
        else:
            print(f"  Total Chunks: {result['num_chunks']}")
            docs = result['chunks']
            if docs:
                # Show details of first and last chunk
                first_doc = docs[0]
                last_doc = docs[-1]
                print(f"  First Chunk Length (chars): {len(first_doc.page_content)}")
                print(f"  First Chunk Preview: {first_doc.page_content[:150]}...")
                print(f"  Last Chunk Length (chars): {len(last_doc.page_content)}")
                print(f"  Last Chunk Preview: {last_doc.page_content[:150]}...")



# --- Select two chunking methods to work with ---
# You can change these names if you prefer different methods
selected_method_1 = "Recursive" # Example: "Fixed Size", "Recursive", "Sentence"
selected_method_2 = "Sentence"  # Example: "Fixed Size", "Recursive", "Sentence"

print(f"Selected Chunking Methods for Embedding:")
print(f"1. {selected_method_1}")
print(f"2. {selected_method_2}")

# Check if the selected methods were processed successfully
if selected_method_1 not in chunked_results or selected_method_2 not in chunked_results:
    print(" Error: One or both selected methods were not found in chunked_results.")
    print("Please check the chunking step or adjust the selected method names above.")
else:
    if 'error' in chunked_results[selected_method_1]:
        print(f"Error with {selected_method_1}: {chunked_results[selected_method_1]['error']}")
    if 'error' in chunked_results[selected_method_2]:
        print(f"Error with {selected_method_2}: {chunked_results[selected_method_2]['error']}")

    # Proceed only if both methods are okay
    if ('error' not in chunked_results[selected_method_1]) and ('error' not in chunked_results[selected_method_2]):
        print(" Both selected methods are ready for embedding.")
        # Extract the chunked documents (LangChain Document objects)
        chunks_method_1 = chunked_results[selected_method_1]['chunks']
        chunks_method_2 = chunked_results[selected_method_2]['chunks']

        # Extract the actual text content from the LangChain Document objects
        texts_method_1 = [doc.page_content for doc in chunks_method_1]
        texts_method_2 = [doc.page_content for doc in chunks_method_2]

        print(f" Extracted text chunks for {selected_method_1}: {len(texts_method_1)} chunks")
        print(f" Extracted text chunks for {selected_method_2}: {len(texts_method_2)} chunks")

    else:
        print(" Cannot proceed due to errors in selected chunking methods.")
        # Stop or handle error appropriately


# --- Install the SentenceTransformer library ---
# This library is needed to generate embeddings
!pip install -q sentence-transformers # -q for quiet installation
print(" sentence-transformers library installed.")


# --- Import SentenceTransformer ---
from sentence_transformers import SentenceTransformer

# --- Load a pre-trained SentenceTransformer model ---
# You can choose different models. 'all-MiniLM-L6-v2' is a good, fast, and effective general-purpose model.
# Others: 'all-mpnet-base-v2' (more powerful but slower), 'paraphrase-MiniLM-L6-v2', etc.
model_name = 'all-MiniLM-L6-v2'
print(f"Loading SentenceTransformer model: {model_name}...")
embedding_model = SentenceTransformer(model_name)
print(" Model loaded successfully.")

# --- Generate Embeddings for Selected Methods ---
embeddings_method_1 = None
embeddings_method_2 = None

try:
    print(f"Generating embeddings for {selected_method_1} chunks...")
    embeddings_method_1 = embedding_model.encode(texts_method_1)
    print(f" Embeddings generated for {selected_method_1}. Shape: {embeddings_method_1.shape}")

    print(f"Generating embeddings for {selected_method_2} chunks...")
    embeddings_method_2 = embedding_model.encode(texts_method_2)
    print(f"Embeddings generated for {selected_method_2}. Shape: {embeddings_method_2.shape}")

except Exception as e:
    print(f" Error generating embeddings: {e}")

# --- (Optional) Inspect Embeddings ---
# You can now use these embeddings for similarity search, clustering, etc.
if embeddings_method_1 is not None:
    print(f"\n--- Sample Embedding for {selected_method_1} (First Chunk) ---")
    print(f"Type: {type(embeddings_method_1[0])}")
    print(f"Length: {len(embeddings_method_1[0])}")
    # Print first 10 values as an example
    print(f"First 10 values: {embeddings_method_1[0][:10]}")

if embeddings_method_2 is not None:
    print(f"\n--- Sample Embedding for {selected_method_2} (First Chunk) ---")
    print(f"Type: {type(embeddings_method_2[0])}")
    print(f"Length: {len(embeddings_method_2[0])}")
    # Print first 10 values as an example
    print(f"First 10 values: {embeddings_method_2[0][:10]}")

