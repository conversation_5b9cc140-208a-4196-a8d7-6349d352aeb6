{"cells": [{"cell_type": "markdown", "metadata": {"id": "e5b9a7cb"}, "source": ["## Setup\n", "\n", "First, we need to install the necessary library and set up the Groq API client.\n", "\n", "Make sure you have your Groq API key stored in Colab secrets under the name `GROQ_API_KEY`."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "75e21770", "outputId": "8b9e746c-9857-4e1e-cd91-54e4844a91bc"}, "source": ["!pip install groq ipywidgets"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting groq\n", "  Downloading groq-0.30.0-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: ipywidgets in /usr/local/lib/python3.11/dist-packages (7.7.1)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from groq) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from groq) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from groq) (0.28.1)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from groq) (2.11.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in /usr/local/lib/python3.11/dist-packages (from groq) (4.14.1)\n", "Requirement already satisfied: ipykernel>=4.5.1 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (6.17.1)\n", "Requirement already satisfied: ipython-genutils~=0.2.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (0.2.0)\n", "Requirement already satisfied: traitlets>=4.3.1 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (5.7.1)\n", "Requirement already satisfied: widgetsnbextension~=3.6.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (3.6.10)\n", "Requirement already satisfied: ipython>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (7.34.0)\n", "Requirement already satisfied: jupyterlab-widgets>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from ipywidgets) (3.0.15)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->groq) (3.10)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (2025.7.14)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->groq) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq) (0.16.0)\n", "Requirement already satisfied: debugpy>=1.0 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (1.8.15)\n", "Requirement already satisfied: jupyter-client>=6.1.12 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (6.1.12)\n", "Requirement already satisfied: matplotlib-inline>=0.1 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (0.1.7)\n", "Requirement already satisfied: nest-asyncio in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (1.6.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (25.0)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (5.9.5)\n", "Requirement already satisfied: pyzmq>=17 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (24.0.1)\n", "Requirement already satisfied: tornado>=6.1 in /usr/local/lib/python3.11/dist-packages (from ipykernel>=4.5.1->ipywidgets) (6.4.2)\n", "Requirement already satisfied: setuptools>=18.5 in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (75.2.0)\n", "Collecting jedi>=0.16 (from ipython>=4.0.0->ipywidgets)\n", "  Downloading jedi-0.19.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Requirement already satisfied: decorator in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (4.4.2)\n", "Requirement already satisfied: pickleshare in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (0.7.5)\n", "Requirement already satisfied: prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (3.0.51)\n", "Requirement already satisfied: pygments in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (2.19.2)\n", "Requirement already satisfied: backcall in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (0.2.0)\n", "Requirement already satisfied: pexpect>4.3 in /usr/local/lib/python3.11/dist-packages (from ipython>=4.0.0->ipywidgets) (4.9.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->groq) (0.4.1)\n", "Requirement already satisfied: notebook>=4.4.1 in /usr/local/lib/python3.11/dist-packages (from widgetsnbextension~=3.6.0->ipywidgets) (6.5.7)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /usr/local/lib/python3.11/dist-packages (from jedi>=0.16->ipython>=4.0.0->ipywidgets) (0.8.4)\n", "Requirement already satisfied: jupyter-core>=4.6.0 in /usr/local/lib/python3.11/dist-packages (from jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (5.8.1)\n", "Requirement already satisfied: python-dateutil>=2.1 in /usr/local/lib/python3.11/dist-packages (from jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (2.9.0.post0)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (3.1.6)\n", "Requirement already satisfied: argon2-cffi in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (25.1.0)\n", "Requirement already satisfied: nbformat in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (5.10.4)\n", "Requirement already satisfied: nbconvert>=5 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (7.16.6)\n", "Requirement already satisfied: Send2Trash>=1.8.0 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.8.3)\n", "Requirement already satisfied: terminado>=0.8.3 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.18.1)\n", "Requirement already satisfied: prometheus-client in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.22.1)\n", "Requirement already satisfied: nbclassic>=0.4.7 in /usr/local/lib/python3.11/dist-packages (from notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.3.1)\n", "Requirement already satisfied: ptyprocess>=0.5 in /usr/local/lib/python3.11/dist-packages (from pexpect>4.3->ipython>=4.0.0->ipywidgets) (0.7.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.11/dist-packages (from prompt-toolkit!=3.0.0,!=3.0.1,<3.1.0,>=2.0.0->ipython>=4.0.0->ipywidgets) (0.2.13)\n", "Requirement already satisfied: platformdirs>=2.5 in /usr/local/lib/python3.11/dist-packages (from jupyter-core>=4.6.0->jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (4.3.8)\n", "Requirement already satisfied: notebook-shim>=0.2.3 in /usr/local/lib/python3.11/dist-packages (from nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.2.4)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (4.13.4)\n", "Requirement already satisfied: bleach!=5.0.0 in /usr/local/lib/python3.11/dist-packages (from bleach[css]!=5.0.0->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (6.2.0)\n", "Requirement already satisfied: defusedxml in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.7.1)\n", "Requirement already satisfied: jupyterlab-pygments in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.3.0)\n", "Requirement already satisfied: markupsafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (3.0.2)\n", "Requirement already satisfied: mistune<4,>=2.0.3 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (3.1.3)\n", "Requirement already satisfied: nbclient>=0.5.0 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.10.2)\n", "Requirement already satisfied: pandocfilters>=1.4.1 in /usr/local/lib/python3.11/dist-packages (from nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.5.1)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /usr/local/lib/python3.11/dist-packages (from nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2.21.1)\n", "Requirement already satisfied: jsonschema>=2.6 in /usr/local/lib/python3.11/dist-packages (from nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (4.25.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.1->jupyter-client>=6.1.12->ipykernel>=4.5.1->ipywidgets) (1.17.0)\n", "Requirement already satisfied: argon2-cffi-bindings in /usr/local/lib/python3.11/dist-packages (from argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (21.2.0)\n", "Requirement already satisfied: webencodings in /usr/local/lib/python3.11/dist-packages (from bleach!=5.0.0->bleach[css]!=5.0.0->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.5.1)\n", "Requirement already satisfied: tinycss2<1.5,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from bleach[css]!=5.0.0->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.4.0)\n", "Requirement already satisfied: attrs>=22.2.0 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=2.6->nbformat->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (0.26.0)\n", "Requirement already satisfied: jupyter-server<3,>=1.8 in /usr/local/lib/python3.11/dist-packages (from notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.16.0)\n", "Requirement already satisfied: cffi>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.17.1)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.11/dist-packages (from beautifulsoup4->nbconvert>=5->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2.7)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.11/dist-packages (from cffi>=1.0.1->argon2-cffi-bindings->argon2-cffi->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (2.22)\n", "Requirement already satisfied: websocket-client in /usr/local/lib/python3.11/dist-packages (from jupyter-server<3,>=1.8->notebook-shim>=0.2.3->nbclassic>=0.4.7->notebook>=4.4.1->widgetsnbextension~=3.6.0->ipywidgets) (1.8.0)\n", "Downloading groq-0.30.0-py3-none-any.whl (131 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m131.1/131.1 kB\u001b[0m \u001b[31m4.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jedi-0.19.2-py2.py3-none-any.whl (1.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m27.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: jedi, groq\n", "Successfully installed groq-0.30.0 jedi-0.19.2\n"]}]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ac9ea762", "outputId": "c46529df-8394-45e2-dc19-665a2a01cc0c"}, "source": ["import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "from groq import Groq\n", "import os\n", "from google.colab import userdata, files\n", "\n", "# Initialize Groq client using Colab secrets\n", "api_key = userdata.get('GROQ_API_KEY')\n", "client = Groq(api_key=api_key)\n", "print(\"API key successfully loaded from Colab secrets\")"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["API key successfully loaded from Colab secrets\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "f8ef1137"}, "source": ["## Upload Articles\n", "\n", "Now, upload the three text files containing your research articles."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 212}, "id": "966f71ac", "outputId": "67c9989d-a815-4c06-c3bb-7da48d7a2d20"}, "source": ["# Upload files widget\n", "print(\"Please upload exactly 3 text files containing research articles:\")\n", "uploaded = files.upload()\n", "\n", "# Store file contents and filenames\n", "file_contents = []\n", "filenames = []\n", "\n", "if len(uploaded) != 3:\n", "    print(\"Please upload exactly 3 files\")\n", "else:\n", "    for filename, content in uploaded.items():\n", "        article_text = content.decode('utf-8')\n", "        file_contents.append(article_text)\n", "        filenames.append(filename)\n", "        print(f\"Loaded: {filename} ({len(article_text.split())} words)\")\n", "\n", "# A simple validation function (no length restriction)\n", "def validate_article_length(article, filename):\n", "    # No length restriction for this example\n", "    return None"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Please upload exactly 3 text files containing research articles:\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "     <input type=\"file\" id=\"files-8d54e554-e03c-4939-894b-58100c2d5bfd\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-8d54e554-e03c-4939-894b-58100c2d5bfd\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Saving ChatGPT Search A Guide With Example.txt to ChatGPT Search A Guide With Example.txt\n", "Saving Context Engineering A Guide With Ex.txt to Context Engineering A Guide With Ex.txt\n", "Saving Small Language Models A Guide With.txt to Small Language Models A Guide With.txt\n", "Loaded: ChatGPT Search A Guide With Example.txt (2203 words)\n", "Loaded: Context Engineering A Guide With Ex.txt (2085 words)\n", "Loaded: Small Language Models A Guide With.txt (2457 words)\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "4d1baa8f"}, "source": ["## Define Prompting Functions\n", "\n", "We'll define functions for zero-shot, one-shot, and few-shot prompting based on the desired summary format."]}, {"cell_type": "code", "metadata": {"id": "8a746bf3"}, "source": ["# Zero-shot prompt with explicit headings\n", "def zero_shot_prompt(article):\n", "    return f\"\"\"\n", "    Summarize the following research article in a single paragraph of 150 words or less. Include the following sections:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "\n", "    Article:\n", "    {article}\n", "    \"\"\"\n", "\n", "# One-shot prompt with explicit headings (uses first article's summary as example)\n", "def one_shot_prompt(article, example_article, example_summary):\n", "    return f\"\"\"\n", "    Summarize the following research article in a single paragraph of 150 words or less. Include the following sections:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "\n", "    Example Article:\n", "    {example_article}\n", "\n", "    Example Summary:\n", "    {example_summary}\n", "\n", "    Now summarize this article, including the explicit headings for each section:\n", "    {article}\n", "    \"\"\"\n", "\n", "# Few-shot prompt with explicit headings (uses first two articles and their summaries as examples)\n", "def few_shot_prompt(article, examples):\n", "    prompt = \"\"\"\n", "    Summarize the following research article in a single paragraph of 150 words or less. Include the following sections:\n", "    Background/introduction: [Content]\n", "    Research objective: [Content]\n", "    Methodology: [Content]\n", "    Key results: [Content]\n", "    Main takeaways/conclusions: [Content]\n", "\n", "    \"\"\"\n", "\n", "    for i, (ex_article, ex_summary) in enumerate(examples):\n", "        prompt += f\"\\nExample {i+1}:\\nArticle: {ex_article}\\nSummary: {ex_summary}\\n\"\n", "\n", "    prompt += f\"\\nNow summarize this article, including the explicit headings for each section:\\n{article}\"\n", "    return prompt"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "38324d83"}, "source": ["## Define API Call Function\n", "\n", "This function sends the prompt to the Groq API and returns the generated summary. We will use the `meta-llama/llama-4-scout-17b-16e-instruct` model as requested."]}, {"cell_type": "code", "metadata": {"id": "288fa0fe"}, "source": ["# Function to call Groq API\n", "def get_summary(prompt):\n", "    completion = client.chat.completions.create(\n", "        model=\"meta-llama/llama-4-scout-17b-16e-instruct\", # Using the requested model\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        temperature=0.7,\n", "        max_completion_tokens=200, # Keep max tokens for summary consistent\n", "        top_p=1,\n", "        stream=False,\n", "        stop=None,\n", "    )\n", "    return completion.choices[0].message.content.strip()\n", "\n", "# Function to validate word count of the summary\n", "def validate_summary(summary):\n", "    word_count = len(summary.split())\n", "    if word_count > 150:\n", "        return f\"Word count: {word_count}\"\n", "    else:\n", "        return f\"Word count: {word_count}\""], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "dd275c5c"}, "source": ["## Process Articles Sequentially\n", "\n", "This section sets up the UI and the logic to process the uploaded articles sequentially using the defined prompting techniques and save the output."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["8da221bd53d84ed5ab50ef6864850f6b", "47971cb18a7a449fa04d430dc76d3928", "ab71dc67194d4060adc01fb4ca5bd91b", "bb0a41665036416c9902d8f060b27634", "8e5bfa3459864c57ba505d3b4532f871", "51f76d7284fa4cfeb0d0ebef5f197dda", "20379cc48ed144f693a7803a9cb4bbea"]}, "id": "48de1b78", "outputId": "4c420bfb-d82e-4320-a922-c6348b87408b"}, "source": ["# Create UI widgets\n", "process_button = widgets.Button(\n", "    description='Process Articles Sequentially and Save Output',\n", "    button_style='success',\n", "    tooltip='Process all three articles using sequential prompting techniques and save output to a file'\n", ")\n", "\n", "output_area = widgets.Output()\n", "\n", "# Display UI\n", "display(widgets.VBox([process_button, output_area]))\n", "\n", "# Button click handler for sequential processing and saving\n", "def on_process_clicked(b):\n", "    if len(file_contents) != 3:\n", "        with output_area:\n", "            clear_output()\n", "            print(\"Please upload exactly 3 files first\")\n", "        return\n", "\n", "    # Validate all articles first (no length validation now)\n", "    validation_errors = []\n", "    for i, (article, filename) in enumerate(zip(file_contents, filenames)):\n", "        error = validate_article_length(article, filename)\n", "        if error:\n", "            validation_errors.append(error)\n", "\n", "    if validation_errors:\n", "        with output_area:\n", "            clear_output()\n", "            print(\"Article validation errors:\")\n", "            for error in validation_errors:\n", "                print(error)\n", "        return\n", "\n", "    output_text = \"\"\n", "    with output_area:\n", "        clear_output()\n", "        print(\"Starting sequential processing of articles...\\n\")\n", "\n", "        # Store summaries for use in subsequent prompts\n", "        summaries = []\n", "\n", "        # Process Article 1 with Zero-shot\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        output_text += f\"Processing Article 1: {filenames[0]}\\n\"\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        print(\"=\"*60)\n", "        print(f\"Processing Article 1: {filenames[0]}\")\n", "        print(\"=\"*60)\n", "        zero_shot = zero_shot_prompt(file_contents[0])\n", "        summary1 = get_summary(zero_shot)\n", "        summaries.append(summary1)\n", "\n", "        output_text += \"Zero-shot Summary:\\n\"\n", "        output_text += summary1 + \"\\n\"\n", "        output_text += validate_summary(summary1) + \"\\n\\n\"\n", "        print(\"Zero-shot Summary:\")\n", "        print(summary1)\n", "        print(validate_summary(summary1))\n", "        print(\"\\n\")\n", "\n", "        # Process Article 2 with One-shot (using Article 1's summary as example)\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        output_text += f\"Processing Article 2: {filenames[1]}\\n\"\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        print(\"=\"*60)\n", "        print(f\"Processing Article 2: {filenames[1]}\")\n", "        print(\"=\"*60)\n", "        one_shot = one_shot_prompt(file_contents[1], file_contents[0], summary1)\n", "        summary2 = get_summary(one_shot)\n", "        summaries.append(summary2)\n", "\n", "        output_text += \"One-shot Summary:\\n\" # Simplified heading\n", "        output_text += summary2 + \"\\n\"\n", "        output_text += validate_summary(summary2) + \"\\n\\n\"\n", "        print(\"One-shot Summary:\") # Simplified heading\n", "        print(summary2)\n", "        print(validate_summary(summary2))\n", "        print(\"\\n\")\n", "\n", "\n", "        # Process Article 3 with Few-shot (using Articles 1&2 and their summaries)\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        output_text += f\"Processing Article 3: {filenames[2]}\\n\"\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        print(\"=\"*60)\n", "        print(f\"Processing Article 3: {filenames[2]}\")\n", "        print(\"=\"*60)\n", "        examples = [(file_contents[0], summary1), (file_contents[1], summary2)]\n", "        few_shot = few_shot_prompt(file_contents[2], examples)\n", "        summary3 = get_summary(few_shot)\n", "        summaries.append(summary3)\n", "\n", "        output_text += \"Few-shot Summary:\\n\" # Simplified heading\n", "        output_text += summary3 + \"\\n\"\n", "        output_text += validate_summary(summary3) + \"\\n\\n\"\n", "        print(\"Few-shot Summary:\") # Simplified heading\n", "        print(summary3)\n", "        print(validate_summary(summary3))\n", "        print(\"\\n\")\n", "\n", "\n", "        # Final summary of all approaches\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        output_text += \"FINAL SUMMARY OF ALL APPROACHES\\n\"\n", "        output_text += \"=\"*60 + \"\\n\"\n", "        print(\"=\"*60)\n", "        print(\"FINAL SUMMARY OF ALL APPROACHES\")\n", "        print(\"=\"*60)\n", "\n", "        output_text += f\"Article 1 ({filenames[0]}): Zero-shot approach\\n\"\n", "        output_text += f\"Word count: {len(file_contents[0].split())}\\n\"\n", "        output_text += f\"Summary word count: {len(summary1.split())}\\n\\n\"\n", "        print(f\"Article 1 ({filenames[0]}): Zero-shot approach\")\n", "        print(f\"Word count: {len(file_contents[0].split())}\")\n", "        print(f\"Summary word count: {len(summary1.split())}\")\n", "        print(\"\\n\" + \"-\"*40 + \"\\n\")\n", "\n", "        output_text += f\"Article 2 ({filenames[1]}): One-shot approach (with Article 1 example)\\n\"\n", "        output_text += f\"Word count: {len(file_contents[1].split())}\\n\"\n", "        output_text += f\"Summary word count: {len(summary2.split())}\\n\\n\"\n", "        print(f\"Article 2 ({filenames[1]}): One-shot approach (with Article 1 example)\")\n", "        print(f\"Word count: {len(file_contents[1].split())}\")\n", "        print(f\"Summary word count: {len(summary2.split())}\")\n", "        print(\"\\n\" + \"-\"*40 + \"\\n\")\n", "\n", "        output_text += f\"Article 3 ({filenames[2]}): Few-shot approach (with Articles 1&2 examples)\\n\"\n", "        output_text += f\"Word count: {len(file_contents[2].split())}\\n\"\n", "        output_text += f\"Summary word count: {len(summary3.split())}\\n\"\n", "        output_text += \"\\n\" + \"=\"*60\n", "        print(f\"Article 3 ({filenames[2]}): Few-shot approach (with Articles 1&2 examples)\")\n", "        print(f\"Word count: {len(file_contents[2].split())}\")\n", "        print(f\"Summary word count: {len(summary3.split())}\")\n", "        print(\"\\n\" + \"=\"*60)\n", "\n", "    # Save output to a text file\n", "    output_filename = \"sequential_summaries.txt\"\n", "    with open(output_filename, \"w\") as f:\n", "        f.write(output_text)\n", "\n", "    with output_area:\n", "        print(f\"\\nOutput saved to '{output_filename}'\")\n", "\n", "\n", "# Attach event handler\n", "process_button.on_click(on_process_clicked)"], "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["VBox(children=(But<PERSON>(button_style='success', description='Process Articles Sequentially and Save Output', sty…"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "8da221bd53d84ed5ab50ef6864850f6b"}}, "metadata": {}}]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"8da221bd53d84ed5ab50ef6864850f6b": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_47971cb18a7a449fa04d430dc76d3928", "IPY_MODEL_ab71dc67194d4060adc01fb4ca5bd91b"], "layout": "IPY_MODEL_bb0a41665036416c9902d8f060b27634"}}, "47971cb18a7a449fa04d430dc76d3928": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ButtonView", "button_style": "success", "description": "Process Articles Sequentially and Save Output", "disabled": false, "icon": "", "layout": "IPY_MODEL_8e5bfa3459864c57ba505d3b4532f871", "style": "IPY_MODEL_51f76d7284fa4cfeb0d0ebef5f197dda", "tooltip": "Process all three articles using sequential prompting techniques and save output to a file"}}, "ab71dc67194d4060adc01fb4ca5bd91b": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_20379cc48ed144f693a7803a9cb4bbea", "msg_id": "", "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Starting sequential processing of articles...\n", "\n", "============================================================\n", "Processing Article 1: ChatGPT Search A Guide With Example.txt\n", "============================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Zero-shot Summary:\n", "Here is a summary of the research article in a single paragraph of 150 words or less, including the requested sections:\n", "\n", "**Background/Introduction**: OpenAI has introduced ChatGPT Search, a new feature that integrates search capabilities into the ChatGPT interface. \n", "**Research Objective**: The objective is to explore how ChatGPT Search works, its features, and its potential as a search tool. \n", "**Methodology**: The article provides a comprehensive guide on accessing ChatGPT Search, its features, and how it handles different types of search queries, including informational, navigational, commercial, and transactional queries. \n", "**Key results**: ChatGPT Search provides detailed answers, source citations, and visual designs, leveraging partnerships with major news and data providers. \n", "**Main takeaways/conclusions**: ChatGPT Search offers enhanced features, including real-time information and visually enriched results, making it a potential competitor to traditional search engines like Google Search. However, there is room for improvement, particularly in using advanced features consistently and providing\n", "Word count: 147\n", "\n", "\n", "============================================================\n", "Processing Article 2: Context Engineering A Guide With Ex.txt\n", "============================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["One-shot Summary:\n", "Here is a summary of the article in a single paragraph of 150 words or less, including the requested sections:\n", "\n", "**Background/Introduction**: Context engineering is the practice of designing systems that decide what information an AI model sees before generating a response. It involves managing different types of information that make up the full context.\n", "\n", "**Research Objective**: The objective is to explain what context engineering is, how it works, and when to use it instead of regular prompt engineering.\n", "\n", "**Methodology**: The article discusses the principles behind context engineering, its differences from prompt engineering, and practical techniques for implementing it.\n", "\n", "**Key results**: Context engineering is essential for building AI applications that need to work with complex, interconnected information, such as conversational AI, document analysis tools, and coding assistants.\n", "\n", "**Main takeaways/conclusions**: Context engineering techniques, such as RAG systems, context validation, and tool management, are crucial for maintaining relevant context across multiple interactions and creating AI systems that feel intelligent and aware.\n", "Word count: 158\n", "\n", "\n", "============================================================\n", "Processing Article 3: Small Language Models A Guide With.txt\n", "============================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Few-shot Summary:\n", "Here is a summary of the article in a single paragraph of 150 words or less, including the requested sections:\n", "\n", "**Background/Introduction**: Small language models (SLMs) are compact, efficient versions of large language models (LLMs), with fewer parameters (typically under 10 billion). \n", "**Research Objective**: The objective is to explain what SLMs are, their characteristics, and their applications. \n", "**Methodology**: The article discusses the principles behind SLMs, their differences from LLMs, and practical techniques for implementing them, including distillation, pruning, and quantization. \n", "**Key results**: SLMs are efficient, accessible, and customizable, making them suitable for specific tasks like customer support, healthcare, and education. They can run on devices with limited resources, enabling on-device AI and offline applications. \n", "**Main takeaways/conclusions**: SLMs offer a balance between performance and resource efficiency, making them a better choice for simpler tasks, resource-limited environments, and on-device AI applications,\n", "Word count: 138\n", "\n", "\n", "============================================================\n", "FINAL SUMMARY OF ALL APPROACHES\n", "============================================================\n", "Article 1 (ChatGPT Search A Guide With Example.txt): Zero-shot approach\n", "Word count: 2203\n", "Summary word count: 147\n", "\n", "----------------------------------------\n", "\n", "Article 2 (Context Engineering A Guide With Ex.txt): One-shot approach (with Article 1 example)\n", "Word count: 2085\n", "Summary word count: 158\n", "\n", "----------------------------------------\n", "\n", "Article 3 (Small Language Models A Guide With.txt): Few-shot approach (with Articles 1&2 examples)\n", "Word count: 2457\n", "Summary word count: 138\n", "\n", "============================================================\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Output saved to 'sequential_summaries.txt'\n"]}]}}, "bb0a41665036416c9902d8f060b27634": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8e5bfa3459864c57ba505d3b4532f871": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "51f76d7284fa4cfeb0d0ebef5f197dda": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_color": null, "font_weight": ""}}, "20379cc48ed144f693a7803a9cb4bbea": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 0}